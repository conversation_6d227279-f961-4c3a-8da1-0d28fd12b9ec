<template>
  <div class="food-map-view">
    <!-- 移动端左侧工具栏 -->
    <div v-if="isMobile" class="left-toolbar">
      <el-button :icon="Menu" @click="toggleSidebar" circle />
      <el-button type="primary" :icon="Plus" @click="addShop" circle />
    </div>

    <!-- 主要布局 -->
    <div class="main-layout" :class="{ mobile: isMobile }">
      <!-- 侧边栏 -->
      <div class="sidebar" :class="{
        collapsed: sidebarCollapsed,
        mobile: isMobile,
      }">
        <!-- 侧边栏展开折叠按钮 -->
        <el-button class="sidebar-button" :icon="sidebarCollapsed ? ArrowLeft : ArrowRight" @click="toggleSidebar" text />
        <!-- 桌面端头部 -->
        <div v-if="!isMobile" class="sidebar-header" :class="{ collapsed: sidebarCollapsed }">
          <h2 v-if="!sidebarCollapsed">未境美食地图</h2>
        </div>

        <!-- 侧边栏内容 -->
        <div class="sidebar-content" v-show="!sidebarCollapsed || isMobile">
          <!-- 快速操作 -->
          <div class="quick-actions">
            <el-button type="primary" @click="addShop" style="width: 100%">
              <el-icon>
                <Plus />
              </el-icon>
              添加店铺
            </el-button>
          </div>

          <!-- 分类筛选 -->
          <div class="category-filter-header">
            <h3>分类筛选</h3>
            <el-button
              :icon="categoryFilterCollapsed ? ArrowDown : ArrowUp"
              @click="toggleCategoryFilter"
              circle
              size="small"
            />
          </div>
          <CategoryFilter v-if="!categoryFilterCollapsed" />

          <!-- 店铺列表 -->
          <div class="shop-list-container">
            <ShopList />
          </div>
        </div>
      </div>

      <!-- 地图区域 -->
      <div class="map-area">
        <MapView />
      </div>
    </div>

    <!-- 弹窗组件 -->
    <ShopForm />
    <CategoryForm />

    <!-- 移动端遮罩 -->
    <div v-if="isMobile && !sidebarCollapsed" class="mobile-overlay" @click="closeSidebar"></div>
  </div>
</template>

<script>
import { computed, onMounted, onUnmounted, ref } from "vue";
import { useStore } from "vuex";
import { Menu, Plus, ArrowLeft, ArrowRight, Fold, ArrowDown, ArrowUp } from "@element-plus/icons-vue";

// 导入组件
import MapView from "@/components/MapView.vue";
import CategoryFilter from "@/components/CategoryFilter.vue";
import ShopList from "@/components/ShopList.vue";
import ShopForm from "@/components/ShopForm.vue";
import CategoryForm from "@/components/CategoryForm.vue";

export default {
  name: "FoodMapView",
  components: {
    MapView,
    CategoryFilter,
    ShopList,
    ShopForm,
    CategoryForm,
  },

  setup() {
    const store = useStore();

    // 计算属性
    const sidebarCollapsed = computed(
      () => store.getters["ui/sidebarCollapsed"]
    );
    const isMobile = computed(() => store.getters["ui/isMobile"]);
    
    // 分类筛选折叠状态
    const categoryFilterCollapsed = ref(false);
    const toggleCategoryFilter = () => {
      categoryFilterCollapsed.value = !categoryFilterCollapsed.value;
    };

    // 切换侧边栏
    const toggleSidebar = () => {
      store.dispatch("ui/toggleSidebar");
    };

    // 关闭侧边栏（移动端）
    const closeSidebar = () => {
      if (isMobile.value) {
        store.dispatch("ui/setSidebarCollapsed", true);
      }
    };

    // 添加店铺
    const addShop = () => {
      store.dispatch("ui/showShopForm");
    };

    // 窗口大小变化处理
    const handleResize = () => {
      store.dispatch("ui/detectMobile");
    };

    onMounted(() => {
      // 初始化检测移动端
      store.dispatch("ui/detectMobile");

      // 监听窗口大小变化
      window.addEventListener("resize", handleResize);
    });

    onUnmounted(() => {
      window.removeEventListener("resize", handleResize);
    });

    return {
      sidebarCollapsed,
      isMobile,
      toggleSidebar,
      closeSidebar,
      addShop,
      toggleCategoryFilter,
      categoryFilterCollapsed,
      Menu,
      Plus,
      ArrowLeft,
      ArrowRight,
      ArrowDown,
      ArrowUp,
    };
  },
};
</script>

<style scoped>
.food-map-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.left-toolbar {
  position: fixed;
  top: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 1002;
}

.main-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.main-layout.mobile {
  position: relative;
}

.sidebar:not(.mobile) {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 350px;
  background: white;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
  z-index: 100;
  /* overflow: hidden; */
  will-change: transform;
}

.sidebar-button:not(.mobile) {
  position: absolute;
  top: 50%;
  left: -40px;
  width: 40px;
  height: 40px;
  transform: translateY(-50%);
  background-color: #fff;
  border-radius: 50% 0 0 50%;
}

.sidebar:not(.mobile).collapsed {
  width: 0;
  height: 40px;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  border-radius: 50% 0 0 50%;
}

.sidebar.mobile {
  position: absolute;
  top: 0;
  left: 0;
  right: auto;
  height: 100%;
  width: 300px;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transform: translateX(-100%);
  z-index: 1001;
}

.sidebar.mobile:not(.collapsed) {
  transform: translateX(0);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
  min-height: 60px;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  white-space: nowrap;
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.quick-actions {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.category-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 8px;
  border-bottom: 1px solid #ebeef5;
}

.category-filter-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.shop-list-container {
  flex: 1;
  overflow: hidden;
  padding: 16px;
  height: calc(100% - 60px);
}

.map-area {
  flex: 1;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.mobile-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar:not(.mobile) {
    width: 300px;
  }

  .sidebar.collapsed:not(.mobile) {
    width: 0;
    border-right: none;
  }
}

@media (max-width: 480px) {
  .sidebar.mobile {
    width: 280px;
  }

  .mobile-toolbar {
    padding: 8px 12px;
  }

  .mobile-toolbar h2 {
    font-size: 16px;
  }
}

/* 动画效果 */
.sidebar {
  transition: transform 0.3s ease;
}
</style>
